/**
 * Working PDF Export Component
 * Simple, reliable PDF export using html2pdf.js with proper SSR handling
 */

'use client';

import React, { useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';

interface WorkingPdfExportProps {
  markdown: string;
  filename?: string;
  onExportStart?: () => void;
  onExportComplete?: () => void;
  onExportError?: (error: Error) => void;
}

export const WorkingPdfExport: React.FC<WorkingPdfExportProps> = ({
  markdown,
  filename = 'document.pdf',
  onExportStart,
  onExportComplete,
  onExportError,
}) => {
  const previewRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);

  const exportToPDF = async () => {
    if (!previewRef.current || isExporting) return;

    try {
      setIsExporting(true);
      onExportStart?.();

      // Dynamically import html2pdf.js to avoid SSR issues
      const html2pdf = (await import('html2pdf.js')).default;

      const element = previewRef.current;

      // Configure PDF options
      const options = {
        margin: 0.5,
        filename: filename.endsWith('.pdf') ? filename : `${filename}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          logging: false,
          letterRendering: true
        },
        jsPDF: {
          unit: 'in',
          format: 'a4',
          orientation: 'portrait'
        },
      };

      // Generate and save PDF
      await html2pdf().set(options).from(element).save();

      onExportComplete?.();
    } catch (error) {
      console.error('PDF export failed:', error);
      onExportError?.(error as Error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="working-pdf-export">
      {/* Export Button */}
      <button
        onClick={exportToPDF}
        disabled={isExporting}
        className={`px-4 py-2 rounded-md transition-colors duration-200 flex items-center gap-2 ${
          isExporting
            ? 'bg-gray-400 text-white cursor-not-allowed'
            : 'bg-red-600 text-white hover:bg-red-700'
        }`}
        title="Export as PDF"
      >
        {isExporting ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Exporting...
          </>
        ) : (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export PDF
          </>
        )}
      </button>

      {/* Hidden preview for PDF generation */}
      <div
        ref={previewRef}
        style={{
          position: 'absolute',
          left: '-9999px',
          top: '0',
          background: '#ffffff',
          color: '#000000',
          padding: '40px',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: '14px',
          lineHeight: '1.6',
          maxWidth: '210mm', // A4 width
          width: '210mm',
          minHeight: '297mm', // A4 height
        }}
      >
        <ReactMarkdown
          components={{
            code({ node, inline, className, children, ...props }) {
              return inline ? (
                <code
                  className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono"
                  style={{
                    backgroundColor: '#f3f4f6',
                    padding: '2px 4px',
                    borderRadius: '3px',
                    fontSize: '0.875rem',
                    fontFamily: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
                  }}
                  {...props}
                >
                  {children}
                </code>
              ) : (
                <pre
                  style={{
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #e9ecef',
                    borderRadius: '6px',
                    padding: '16px',
                    margin: '16px 0',
                    overflow: 'auto',
                    fontSize: '0.875rem',
                    lineHeight: '1.45',
                    fontFamily: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
                  }}
                  {...props}
                >
                  <code>{children}</code>
                </pre>
              );
            },
            img({ src, alt, ...props }) {
              return (
                <img 
                  src={src} 
                  alt={alt} 
                  style={{ 
                    maxWidth: '100%', 
                    height: 'auto',
                    display: 'block',
                    margin: '1rem 0'
                  }} 
                  {...props}
                />
              );
            },
            table({ children, ...props }) {
              return (
                <table 
                  style={{
                    width: '100%',
                    borderCollapse: 'collapse',
                    margin: '1rem 0',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    overflow: 'hidden'
                  }}
                  {...props}
                >
                  {children}
                </table>
              );
            },
            th({ children, ...props }) {
              return (
                <th 
                  style={{
                    border: '1px solid #e5e7eb',
                    padding: '0.75rem 1rem',
                    backgroundColor: '#f8fafc',
                    fontWeight: '600',
                    textAlign: 'left',
                    borderBottom: '2px solid #e5e7eb'
                  }}
                  {...props}
                >
                  {children}
                </th>
              );
            },
            td({ children, ...props }) {
              return (
                <td 
                  style={{
                    border: '1px solid #e5e7eb',
                    padding: '0.75rem 1rem',
                    textAlign: 'left',
                    verticalAlign: 'top'
                  }}
                  {...props}
                >
                  {children}
                </td>
              );
            },
            h1({ children, ...props }) {
              return (
                <h1 
                  style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    marginTop: '2rem',
                    marginBottom: '1rem',
                    borderBottom: '2px solid #3b82f6',
                    paddingBottom: '0.5rem',
                    color: '#1f2937'
                  }}
                  {...props}
                >
                  {children}
                </h1>
              );
            },
            h2({ children, ...props }) {
              return (
                <h2 
                  style={{
                    fontSize: '1.5rem',
                    fontWeight: '600',
                    marginTop: '1.5rem',
                    marginBottom: '0.75rem',
                    borderBottom: '1px solid #d1d5db',
                    paddingBottom: '0.25rem',
                    color: '#374151'
                  }}
                  {...props}
                >
                  {children}
                </h2>
              );
            },
            h3({ children, ...props }) {
              return (
                <h3 
                  style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    marginTop: '1.25rem',
                    marginBottom: '0.5rem',
                    color: '#4b5563'
                  }}
                  {...props}
                >
                  {children}
                </h3>
              );
            },
            blockquote({ children, ...props }) {
              return (
                <blockquote 
                  style={{
                    borderLeft: '4px solid #3b82f6',
                    margin: '1.5rem 0',
                    padding: '1rem 1.5rem',
                    backgroundColor: '#f8fafc',
                    fontStyle: 'italic',
                    borderRadius: '0 0.5rem 0.5rem 0'
                  }}
                  {...props}
                >
                  {children}
                </blockquote>
              );
            },
            ul({ children, ...props }) {
              // Remove non-HTML attributes that ReactMarkdown might pass
              const { ordered, ...htmlProps } = props;
              return (
                <ul
                  style={{
                    margin: '1rem 0',
                    paddingLeft: '2rem',
                    listStyleType: 'disc'
                  }}
                  {...htmlProps}
                >
                  {children}
                </ul>
              );
            },
            ol({ children, ...props }) {
              // Remove non-HTML attributes that ReactMarkdown might pass
              const { ordered, ...htmlProps } = props;
              return (
                <ol
                  style={{
                    margin: '1rem 0',
                    paddingLeft: '2rem',
                    listStyleType: 'decimal'
                  }}
                  {...htmlProps}
                >
                  {children}
                </ol>
              );
            },
            li({ children, ...props }) {
              // Remove any non-HTML attributes that ReactMarkdown might pass
              const { ordered, index, ...htmlProps } = props;
              return (
                <li
                  style={{
                    marginBottom: '0.5rem',
                    lineHeight: '1.6'
                  }}
                  {...htmlProps}
                >
                  {children}
                </li>
              );
            },
            p({ children, ...props }) {
              return (
                <p 
                  style={{
                    margin: '1rem 0',
                    lineHeight: '1.6'
                  }}
                  {...props}
                >
                  {children}
                </p>
              );
            }
          }}
        >
          {markdown}
        </ReactMarkdown>
      </div>
    </div>
  );
};
