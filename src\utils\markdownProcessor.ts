/**
 * Clean markdown processing utilities using marked library
 */

'use client';

/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */

import { marked } from 'marked';
import DOMPurify from 'dompurify';

// Configure marked with enhanced options
marked.setOptions({
  gfm: true,
  breaks: true,
  pedantic: false,
  smartypants: true,
  tables: true,
  headerIds: true,
  mangle: false
});

/**
 * Pre-process markdown for custom features that we'll handle manually
 */
function preprocessMarkdown(markdown: string): string {
  return String(markdown || '')
    // Handle marked text (==text==)
    .replace(/==(.*?)==/g, '<mark class="md-highlight">$1</mark>')
    
    // Handle inserted text (++text++)
    .replace(/\+\+(.*?)\+\+/g, '<ins class="md-inserted">$1</ins>')
    
    // Handle subscript and superscript
    .replace(/~([^~\s]+)~/g, '<sub class="md-subscript">$1</sub>')
    .replace(/\^([^\^\s]+)\^/g, '<sup class="md-superscript">$1</sup>')
    
    // Handle keyboard keys ([[key]])
    .replace(/\[\[([^\]]+)\]\]/g, '<kbd class="md-kbd">$1</kbd>')
    
    // Handle task lists
    .replace(/^(\s*)- \[x\]/gm, '$1- <input type="checkbox" checked disabled class="task-list-checkbox">')
    .replace(/^(\s*)- \[ \]/gm, '$1- <input type="checkbox" disabled class="task-list-checkbox">')
    
    // Handle emojis (convert :emoji: to unicode)
    .replace(/:wink:/g, '😉')
    .replace(/:cry:/g, '😢')
    .replace(/:laughing:/g, '😆')
    .replace(/:yum:/g, '😋')
    .replace(/:smile:/g, '😊')
    .replace(/:heart:/g, '❤️')
    .replace(/:thumbsup:/g, '👍')
    .replace(/:thumbsdown:/g, '👎')
    .replace(/:fire:/g, '🔥')
    .replace(/:rocket:/g, '🚀')
    .replace(/:star:/g, '⭐')
    .replace(/:warning:/g, '⚠️')
    .replace(/:info:/g, 'ℹ️')
    .replace(/:check:/g, '✅')
    .replace(/:x:/g, '❌')
    .replace(/:tada:/g, '🎉')
    .replace(/:sparkles:/g, '✨')
    .replace(/:boom:/g, '💥')
    .replace(/:zap:/g, '⚡')
    .replace(/:bulb:/g, '💡')
    
    // Handle emoticons
    .replace(/:-\)/g, '🙂')
    .replace(/:-\(/g, '😞')
    .replace(/8-\)/g, '😎')
    .replace(/;\)/g, '😉')
    .replace(/:D/g, '😃')
    .replace(/:P/g, '😛')
    
    // Handle custom containers
    .replace(/^:::\s*warning\s*(.*?)$/gm, '<div class="custom-container warning"><p class="custom-container-title">$1</p>')
    .replace(/^:::\s*info\s*(.*?)$/gm, '<div class="custom-container info"><p class="custom-container-title">$1</p>')
    .replace(/^:::\s*tip\s*(.*?)$/gm, '<div class="custom-container tip"><p class="custom-container-title">$1</p>')
    .replace(/^:::\s*danger\s*(.*?)$/gm, '<div class="custom-container danger"><p class="custom-container-title">$1</p>')
    .replace(/^:::$/gm, '</div>');
}

/**
 * Add copy buttons to code blocks
 */
function addCopyButtonsToCodeBlocks(html: string): string {
  return html.replace(/<pre(?:\s+class="([^"]*)")?><code(?:\s+class="([^"]*)")?>([\s\S]*?)<\/code><\/pre>/g, (_match, preClass, codeClass, code) => {
    const decodedCode = code
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    // Extract language from class
    const langMatch = (preClass || codeClass || '').match(/language-(\w+)/);
    const language = langMatch ? langMatch[1] : 'text';

    return `
      <div class="code-block-wrapper">
        <div class="code-block-header">
          <span class="code-language">${language}</span>
          <button class="copy-code-btn" data-code="${encodeURIComponent(decodedCode)}">Copy</button>
        </div>
        <pre class="${preClass || `language-${language}`}"><code class="${codeClass || `hljs language-${language}`}">${code}</code></pre>
      </div>
    `;
  });
}

/**
 * Enhance HTML elements with additional classes and features
 */
function enhanceHtmlElements(html: string): string {
  return html
    // Enhance lists with proper styling
    .replace(/<ul>/g, '<ul class="md-list list-disc list-inside space-y-1">')
    .replace(/<ol>/g, '<ol class="md-list list-decimal list-inside space-y-1">')
    .replace(/<li>/g, '<li class="md-list-item">')

    // Enhance task lists
    .replace(/<input type="checkbox"/g, '<input type="checkbox" class="task-list-checkbox"')

    // Enhance abbreviations
    .replace(/<abbr/g, '<abbr class="md-abbr"')

    // Enhance keyboard keys
    .replace(/<kbd>/g, '<kbd class="md-kbd">')

    // Enhance footnotes
    .replace(/<sup class="footnote-ref">/g, '<sup class="footnote-ref md-footnote-ref">')
    .replace(/<section class="footnotes">/g, '<section class="footnotes md-footnotes">')

    // Enhance definition lists
    .replace(/<dl>/g, '<dl class="md-definition-list">')
    .replace(/<dt>/g, '<dt class="md-definition-term">')
    .replace(/<dd>/g, '<dd class="md-definition-description">')

    // Enhance tables with proper responsive wrapper and alignment support
    .replace(/<table>/g, '<div class="table-responsive-wrapper"><table class="markdown-table">')
    .replace(/<\/table>/g, '</table></div>')
    .replace(/<thead>/g, '<thead class="markdown-table-header">')
    .replace(/<tbody>/g, '<tbody class="markdown-table-body">')
    .replace(/<th>/g, '<th class="markdown-table-header-cell">')
    .replace(/<td>/g, '<td class="markdown-table-cell">')

    // Handle table alignment with proper classes
    .replace(/<th class="markdown-table-header-cell" align="right">/g, '<th class="markdown-table-header-cell text-align-right">')
    .replace(/<th class="markdown-table-header-cell" align="center">/g, '<th class="markdown-table-header-cell text-align-center">')
    .replace(/<td class="markdown-table-cell" align="right">/g, '<td class="markdown-table-cell text-align-right">')
    .replace(/<td class="markdown-table-cell" align="center">/g, '<td class="markdown-table-cell text-align-center">')

    // Handle table alignment from markdown syntax (|:---:|, |---:|, |:---|)
    .replace(/style="text-align:\s*center"/g, 'class="text-align-center"')
    .replace(/style="text-align:\s*right"/g, 'class="text-align-right"')
    .replace(/style="text-align:\s*left"/g, 'class="text-align-left"')

    // Enhance blockquotes
    .replace(/<blockquote>/g, '<blockquote class="md-blockquote">')

    // Handle external links to open in new window
    .replace(
      /<a\s+href="([^"]*)"([^>]*)>([\s\S]*?)<\/a>/g,
      (_match, href, attrs, text) => {
        const isExternal = href.startsWith('http') || href.startsWith('//');
        const target = isExternal ? ' target="_blank" rel="noopener noreferrer"' : '';
        return `<a href="${href}"${target}${attrs}>${text}</a>`;
      }
    )

    // Add wrapper classes for better styling
    .replace(/^<p>/gm, '<p class="md-paragraph">')
    .replace(/<hr>/g, '<hr class="md-hr">')

    // Handle strikethrough
    .replace(/<del>/g, '<del class="md-strikethrough">');
}

// Process markdown with enhanced features using marked
function renderMarkdown(markdown: string): string {
  try {
    // Pre-process markdown for custom features
    const processedMarkdown = preprocessMarkdown(markdown);

    // Render with marked
    let html = marked.parse(processedMarkdown) as string;

    // Post-process HTML to add enhanced features
    html = addCopyButtonsToCodeBlocks(html);
    html = enhanceHtmlElements(html);

    return html;
  } catch (error) {
    console.error('Error rendering markdown:', error);
    return '<p>Error rendering markdown</p>';
  }
}

// Process the markdown with custom handling
export function processMarkdown(markdown: string): string {
  try {
    const markdownStr = String(markdown || '');
    return renderMarkdown(markdownStr);
  } catch (error) {
    console.error('Error in processMarkdown:', error);
    return `<p>Error processing markdown: ${String(error)}</p>`;
  }
}

// Export the renderMarkdown function as well for enhanced PDF export
export { renderMarkdown };

// Add delete confirmation handler
export function confirmDelete(message: string = 'Are you sure you want to delete this?'): boolean {
  return window.confirm(message);
}

/**
 * Extract headings from markdown content for table of contents
 */
export function extractHeadings(markdown: string): Array<{ level: number; text: string; id: string }> {
  const headings: Array<{ level: number; text: string; id: string }> = [];
  const lines = markdown.split('\n');

  for (const line of lines) {
    const match = line.match(/^(#{1,6})\s+(.+)$/);
    if (match) {
      const level = match[1].length;
      const text = match[2].trim();
      const id = text
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();

      headings.push({ level, text, id });
    }
  }

  return headings;
}

/**
 * Get word count from markdown
 */
export function getWordCount(markdown: string): {
  words: number;
  characters: number;
  charactersNoSpaces: number;
  paragraphs: number;
  readingTime: number; // in minutes
} {
  // Remove markdown syntax for accurate word count
  const plainText = markdown
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
    .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
    .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered list markers
    .replace(/^\s*>\s+/gm, '') // Remove blockquotes
    .replace(/^\s*\|.*\|$/gm, '') // Remove tables
    .replace(/^\s*[-=]{3,}$/gm, '') // Remove horizontal rules
    .trim();

  const words = plainText.split(/\s+/).filter(word => word.length > 0).length;
  const characters = plainText.length;
  const charactersNoSpaces = plainText.replace(/\s/g, '').length;
  const paragraphs = plainText.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;

  // Average reading speed: 200 words per minute
  const readingTime = Math.ceil(words / 200);

  return {
    words,
    characters,
    charactersNoSpaces,
    paragraphs,
    readingTime
  };
}

/**
 * Validate markdown syntax
 */
export function validateMarkdown(markdown: string): {
  isValid: boolean;
  errors: Array<{ line: number; message: string; type: 'error' | 'warning' }>;
} {
  const errors: Array<{ line: number; message: string; type: 'error' | 'warning' }> = [];
  const lines = markdown.split('\n');

  lines.forEach((line, index) => {
    const lineNumber = index + 1;

    // Check for unmatched brackets
    const openBrackets = (line.match(/\[/g) || []).length;
    const closeBrackets = (line.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
      errors.push({
        line: lineNumber,
        message: 'Unmatched square brackets',
        type: 'warning'
      });
    }

    // Check for unmatched parentheses in links
    const openParens = (line.match(/\(/g) || []).length;
    const closeParens = (line.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push({
        line: lineNumber,
        message: 'Unmatched parentheses',
        type: 'warning'
      });
    }

    // Check for malformed links
    const linkPattern = /\[([^\]]*)\]\(([^)]*)\)/g;
    let match;
    while ((match = linkPattern.exec(line)) !== null) {
      if (!match[2] || match[2].trim() === '') {
        errors.push({
          line: lineNumber,
          message: 'Empty link URL',
          type: 'error'
        });
      }
    }

    // Check for malformed images
    const imagePattern = /!\[([^\]]*)\]\(([^)]*)\)/g;
    while ((match = imagePattern.exec(line)) !== null) {
      if (!match[2] || match[2].trim() === '') {
        errors.push({
          line: lineNumber,
          message: 'Empty image URL',
          type: 'error'
        });
      }
    }
  });

  return {
    isValid: errors.filter(e => e.type === 'error').length === 0,
    errors
  };
}

/**
 * Convert markdown to sanitized HTML using enhanced processor
 */
export function markdownToHtml(markdown: string): string {
  try {
    // Import enhanced processor dynamically to avoid circular dependencies
    const { enhancedMarkdownToHtml } = require('./enhancedMarkdownProcessor');
    return enhancedMarkdownToHtml(markdown);
  } catch (error) {
    console.error('Enhanced markdown processor failed, falling back to basic:', error);

    // Fallback to basic processing
    try {
      const markdownStr = String(markdown || '');
      const html = processMarkdown(markdownStr);

      const cleanHtml = DOMPurify.sanitize(String(html), {
          ALLOWED_TAGS: [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'p', 'br', 'strong', 'em', 'b', 'i', 'u', 'del', 's', 'ins', 'mark', 'sub', 'sup', 'kbd',
            'code', 'pre', 'div', 'span', 'section', 'article', 'aside', 'header', 'footer', 'nav',
            'button', 'a', 'input', 'ul', 'ol', 'li', 'dl', 'dt', 'dd', 'blockquote', 'hr',
            'table', 'thead', 'tbody', 'tr', 'th', 'td', 'img', 'video', 'audio', 'source',
            'abbr', 'details', 'summary'
          ],
          ALLOWED_ATTR: [
            'class', 'id', 'style', 'title', 'lang', 'dir', 'aria-label', 'role',
            'href', 'title', 'rel', 'target', 'src', 'alt', 'width', 'height',
            'controls', 'autoplay', 'loop', 'muted', 'colspan', 'rowspan', 'align', 'valign',
            'type', 'checked', 'disabled', 'data-code', 'data-lang', 'language',
            'data-clipboard-text', 'data-line'
          ],
          ADD_TAGS: ['pre', 'code', 'kbd', 'mark', 'sub', 'sup', 'ins', 'del', 'abbr', 'input'],
          ADD_ATTR: ['class', 'data-code', 'data-clipboard-text', 'data-lang', 'data-line', 'checked', 'disabled', 'type'],
          ALLOW_DATA_ATTR: true,
          USE_PROFILES: { html: true }
      });

      return String(cleanHtml);
    } catch (fallbackError) {
      console.error('Error converting markdown to HTML:', fallbackError);
      return '<p>Error rendering markdown content</p>';
    }
  }
}
