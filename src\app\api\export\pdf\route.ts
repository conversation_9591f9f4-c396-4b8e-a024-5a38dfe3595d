/**
 * Server-side PDF export API route
 * Handles PDF generation using <PERSON><PERSON><PERSON>eer, Playwright, or markdown-pdf
 */

import { NextRequest, NextResponse } from 'next/server';
import { PuppeteerPdfExporter } from '@/utils/puppeteerPdfExport';
import { PlaywrightPdfExporter } from '@/utils/playwrightPdfExport';
import { MarkdownPdfExporter } from '@/utils/markdownPdfExport';

export interface PdfExportRequest {
  markdown: string;
  filename: string;
  method?: 'puppeteer' | 'playwright' | 'markdown-pdf' | 'auto';
  options?: {
    title?: string;
    author?: string;
    quality?: 'high' | 'medium' | 'low';
    format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
    orientation?: 'portrait' | 'landscape';
    margin?: {
      top?: string;
      right?: string;
      bottom?: string;
      left?: string;
    };
  };
}

async function generatePdfWithMethod(
  markdown: string,
  method: 'puppeteer' | 'playwright' | 'markdown-pdf',
  options: Record<string, unknown> = {}
): Promise<{ success: boolean; data?: Uint8Array; error?: string; fileSize?: number }> {
  try {
    let exporter: PuppeteerPdfExporter | PlaywrightPdfExporter | MarkdownPdfExporter;
    let pdfData: Uint8Array;

    switch (method) {
      case 'puppeteer':
        exporter = new PuppeteerPdfExporter(options);
        pdfData = await exporter.exportMarkdownToPdf(markdown);
        await exporter.close();
        break;

      case 'playwright':
        exporter = new PlaywrightPdfExporter(options);
        pdfData = await exporter.exportMarkdownToPdf(markdown);
        await exporter.close();
        break;

      case 'markdown-pdf':
        exporter = new MarkdownPdfExporter(options);
        pdfData = await exporter.exportMarkdownToPdf(markdown);
        break;

      default:
        throw new Error(`Unsupported method: ${method}`);
    }

    return {
      success: true,
      data: pdfData,
      fileSize: pdfData.length,
    };
  } catch (error) {
    console.error(`PDF generation failed with ${method}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

function determineBestMethod(options: Record<string, unknown>): 'puppeteer' | 'playwright' | 'markdown-pdf' {
  // Auto-select the best method based on requirements
  if (options.quality === 'high' || options.tagged || options.outline) {
    return 'playwright';
  }
  
  if (options.quality === 'medium') {
    return 'puppeteer';
  }
  
  return 'markdown-pdf';
}

export async function POST(request: NextRequest) {
  try {
    const body: PdfExportRequest = await request.json();
    
    if (!body.markdown) {
      return NextResponse.json(
        { error: 'Markdown content is required' },
        { status: 400 }
      );
    }

    const method = body.method === 'auto' ? determineBestMethod(body.options || {}) : body.method || 'puppeteer';
    const allMethods: Array<'puppeteer' | 'playwright' | 'markdown-pdf'> = ['playwright', 'puppeteer', 'markdown-pdf'];
    const fallbackMethods = allMethods.filter((m): m is 'puppeteer' | 'playwright' | 'markdown-pdf' => m !== method);

    // Try primary method
    let result = await generatePdfWithMethod(body.markdown, method, body.options);

    // Try fallback methods if primary failed
    if (!result.success) {
      for (const fallbackMethod of fallbackMethods) {
        console.log(`Trying fallback method: ${fallbackMethod}`);
        result = await generatePdfWithMethod(body.markdown, fallbackMethod, body.options);
        
        if (result.success) {
          console.log(`Successfully exported with fallback method: ${fallbackMethod}`);
          break;
        }
      }
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'All PDF export methods failed' },
        { status: 500 }
      );
    }

    // Return PDF as response
    const filename = body.filename.replace(/\.[^/.]+$/, '.pdf');
    
    return new NextResponse(result.data, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': result.fileSize?.toString() || '',
        'X-PDF-Method': method,
        'X-PDF-Size': result.fileSize?.toString() || '',
      },
    });

  } catch (error) {
    console.error('PDF export API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    availableMethods: ['puppeteer', 'playwright', 'markdown-pdf'],
    timestamp: new Date().toISOString(),
  });
}
